{"noir_version": "1.0.0-beta.6+e796dfd67726cbc28eb9991782533b211025928d", "hash": "621137406139641129", "abi": {"parameters": [{"name": "secret_key", "type": {"kind": "field"}, "visibility": "private"}, {"name": "path_elements", "type": {"kind": "array", "length": 20, "type": {"kind": "field"}}, "visibility": "private"}, {"name": "path_indices", "type": {"kind": "array", "length": 20, "type": {"kind": "integer", "sign": "unsigned", "width": 1}}, "visibility": "private"}, {"name": "group_root", "type": {"kind": "field"}, "visibility": "public"}, {"name": "nullifier_hash", "type": {"kind": "field"}, "visibility": "public"}, {"name": "signal_hash", "type": {"kind": "field"}, "visibility": "public"}], "return_type": null, "error_types": {}}, "bytecode": "H4sIAAAAAAAA/9Wb127bQBBFV3F6nB733u24kaIoUe6910+wstL/f0J8s2tAD5snHwNLAgaNMXAwQ56hrN1hxbjj/vFny/9eefzp8ec+f+6O9QdiA4HYYCA2FIgNB2IjgdhoIDYWiI0HYhOB2GQgNhWITQdiM4HYbCA2F4jNB2ILgdiiP3cfr/x5z5+zpF6rtRvVdpqlD0m12SrypJa36kVapHmR22qRZe2iVjSarWYjaaa1rJ128mbWSdzRD7JWuljJ8440VGvyrKP6IEYfUG+78+9oPfXHazTPLOVYia1grM4f4to9ubJK5ZXmiXnB/hgAWWtwf7DeJQ1du37QPfHAZ4JVbqtgvSueV4GdWTfsM+ul3B4EWUnkbuszdQB0UTywn61yWwfrXfM82u20JG4Pgaxq5G6LNwi6KB7Yz1a5pWC9iefRbmclcXsYZNUid/uNcd8ZKRfFA/vZKrcMrLfqebTbeUncHgFZ9cjdfmvc2gflonhgP1vlloP11jyPdrtRErdHQVYRudvvjFvDo1wUD+xnq9waYL11z6PdbpbE7TGQtRG52++NW4umXBQP7Ger3JpgvYXn0W5vlsTtcZC1FbnbH4zbU6FcFA/sZ6vcNsF6NzyPdnu7JG5PgKydyN3+aNzeIOWieGA/W+W2Dda75Xm027slcXsSZO1F7vYn4/a4KRfFA/vZKrddsN4dz6Pd3i+J21Mg6yByt3uNm9WgXOz1LIqn3PbBevc8j3b7sCRuT4Oso8jd/mzczBHlonhgP1vldgjWe+B5tNvHJXF7BmSdRO72F+Nm5ygXxQP72Sq3Y7DeI8+j3T4tiduzIOsscre/GjcDSrkoHtjPVrmdgvWeeB7t9nlJ3J4DWReRu/3NuFlmykXxwH62yu0crPfM82i3L0vi9jzIuorc7e/GzeRTLooH9rNVbpdgvReeR7t9XRK3F0DWTeRu/zDu3RLKRfHAfrbK7Rqs98rzaLdvS+L2Isi6i9ztn8a9I0W5KB7Yz1a53YL13nge7fY97DZ9n38Z994bdV/EA922yu0erPfO8+j7vGRe5hnWA99vsubf4H1eBq9fpatPlrqupeKv/DXV3zWbrBlOzbppJkizE9pj1l6c9iy0tqs1MK0V6DuV/vfUZ7SeZfJc7+fpHSm9S6KZe80ma4ZTs26aCdLshPaYtRc3ady6s9bAtFag71T631Of0XqWLXbda+Wsa7ts/n/8BQtadmlIPAAA", "debug_symbols": "pdXLqoMwEAbgd8nahWZmvPRVDodibVqEoJLqgUPpuzf6N70slKKrX43zEcdorupoDsN5Xzen9qJ2P1d1cLW19Xlv26rs67bxV6+3SIXTfe+M8ZfU27iv6kpnml7tmsHaSP2VdphuunRlM2VfOj8aR8o0R58ePNXWjEe36FUdz5dKwo9i4Ve5fNYn8/WJUAD8YbFVEJoTlp4hTR9AqmefgRdmEHMRZhCnskoowjtIkniVoLOnoAueE7LNb2JRYP0UON86h+3CqtWQEoXVkMYr6jMOE8jy+S+CNrdxmfiqj18TqxqZFaGRuf5cTr/+rKxq9/EzUzr2o5HSCUIjCMEIQaSIDJEjiikICkEhKASFoBAUgkJQCApBYSgMhaEwFIbCUBgKQ2EoDEWgCBSBIlAEikARKAJF8vEr9umZJLuNLXZ1ebDm8dM/DU31tgf0/10YCbtE59rKHAdnxhZPY77pdw==", "file_map": {"50": {"source": "// KRTR Anonymous Membership Circuit\n// Proves membership in authorized group without revealing identity\n\n// Maximum depth for membership tree\nglobal TREE_DEPTH: u32 = 20;\n\nfn main(\n    // Private inputs (not revealed)\n    secret_key: Field,\n    path_elements: [Field; TREE_DEPTH],\n    path_indices: [u1; TREE_DEPTH],\n\n    // Public inputs (revealed)\n    group_root: pub Field,\n    nullifier_hash: pub Field,\n    signal_hash: pub Field\n) {\n    // 1. Verify membership in the group using simple hash\n    let leaf = hash_simple(secret_key, 0);\n    let computed_root = compute_merkle_root(leaf, path_elements, path_indices);\n    assert(computed_root == group_root);\n\n    // 2. Generate nullifier to prevent double-use\n    let computed_nullifier = hash_simple(secret_key, signal_hash);\n    assert(computed_nullifier == nullifier_hash);\n}\n\nfn compute_merkle_root(\n    leaf: Field,\n    path_elements: [Field; TREE_DEPTH],\n    path_indices: [u1; TREE_DEPTH]\n) -> Field {\n    let mut current = leaf;\n\n    for i in 0..TREE_DEPTH {\n        let path_element = path_elements[i];\n        let is_right = path_indices[i];\n\n        let left = if is_right == 1 { path_element } else { current };\n        let right = if is_right == 1 { current } else { path_element };\n\n        current = hash_simple(left, right);\n    }\n\n    current\n}\n\n// Simple hash function for demonstration\nfn hash_simple(a: Field, b: Field) -> Field {\n    // Use a simple but secure hash based on field arithmetic\n    // The modulus is automatically handled by the Field type\n    a * 7 + b * 13 + 42\n}\n", "path": "/Users/<USER>/Desktop/AAA Shibumi Crypto/AAA_KRTR_MESH/krtr-mesh/circuits/membership/src/main.nr"}}, "names": ["main"], "brillig_names": []}