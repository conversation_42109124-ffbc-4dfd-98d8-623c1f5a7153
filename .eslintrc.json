{"extends": ["plugin:@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"es6": true, "node": true, "jest": true, "browser": true}, "rules": {"@typescript-eslint/no-unused-vars": ["warn"], "no-console": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-var-requires": "off"}, "settings": {"import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}, "overrides": [{"files": ["*.ts", "*.tsx"], "rules": {"@typescript-eslint/no-unused-vars": ["error"], "no-undef": "off"}}]}