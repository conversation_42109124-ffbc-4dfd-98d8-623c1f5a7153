#!/bin/bash

# KRTR Circuit Build Script
# Compiles Noir circuits for zero-knowledge proofs

set -e

echo "🔧 Building KRTR Zero-Knowledge Circuits..."

# Check if nargo is installed
if ! command -v nargo &> /dev/null; then
    echo "❌ Nargo not found. Please install Noir toolchain:"
    echo "   curl -L https://raw.githubusercontent.com/noir-lang/noirup/main/install | bash"
    echo "   noirup"
    exit 1
fi

# Create circuits directory if it doesn't exist
mkdir -p circuits

# Build membership circuit
echo "📝 Building membership circuit..."
cd circuits/membership
nargo compile
cd ../..

# Build reputation circuit
echo "📝 Building reputation circuit..."
cd circuits/reputation
nargo compile
cd ../..

# Build message proof circuit
echo "📝 Building message proof circuit..."
cd circuits/message_proof
nargo compile
cd ../..

# Create circuit imports for JavaScript
echo "📦 Creating JavaScript circuit imports..."

# Create circuit imports directory
mkdir -p app/zk/circuits

# Copy compiled circuits to app directory
cp circuits/membership/target/krtr_membership.json app/zk/circuits/membership.json
cp circuits/reputation/target/krtr_reputation.json app/zk/circuits/reputation.json
cp circuits/message_proof/target/krtr_message_proof.json app/zk/circuits/message_proof.json

# Create index file for circuit imports
cat > app/zk/circuits/index.js << 'EOF'
/**
 * KRTR Compiled Circuits
 * Auto-generated by build-circuits.sh
 */

import membershipCircuit from './membership.json';
import reputationCircuit from './reputation.json';
import messageProofCircuit from './message_proof.json';

export {
  membershipCircuit,
  reputationCircuit,
  messageProofCircuit
};
EOF

echo "✅ All circuits compiled successfully!"
echo ""
echo "📊 Circuit Information:"
echo "   - Membership: $(wc -c < app/zk/circuits/membership.json) bytes"
echo "   - Reputation: $(wc -c < app/zk/circuits/reputation.json) bytes"
echo "   - Message Proof: $(wc -c < app/zk/circuits/message_proof.json) bytes"
echo ""
echo "🚀 Circuits are ready for use in KRTR!"
echo ""
echo "💡 Next steps:"
echo "   1. Run 'npm install' to install Noir dependencies"
echo "   2. Test ZK functionality with 'npm run test:zk'"
echo "   3. Deploy to device with 'npm run ios' or 'npm run android'"
