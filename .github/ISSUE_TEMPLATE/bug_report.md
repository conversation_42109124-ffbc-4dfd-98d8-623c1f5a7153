---
name: Bug report
about: Create a report to help us improve KRTR
title: '[BUG] '
labels: 'bug'
assignees: 'Z0rlord'

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Device Information:**
 - Device: [e.g. iPhone 12, Samsung Galaxy S21]
 - OS: [e.g. iOS 15.0, Android 12]
 - KRTR Version: [e.g. 1.0.0]
 - Expo Version: [e.g. 50.0.0]

**Mesh Network Context:**
 - Number of connected peers: [e.g. 3]
 - Network conditions: [e.g. indoor, outdoor, range]
 - Battery level: [e.g. 50%]
 - Power mode: [e.g. balanced]

**Additional context**
Add any other context about the problem here.

**Security Note**
If this bug has security implications, please report it privately via email instead of creating a public issue.
