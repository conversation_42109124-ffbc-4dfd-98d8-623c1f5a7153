name: Security Analysis & Dependency Graph

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security analysis weekly on Mondays at 9 AM UTC
    - cron: '0 9 * * 1'

jobs:
  dependency-analysis:
    name: Dependency Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run npm audit
      run: |
        echo "## NPM Security Audit Results" >> $GITHUB_STEP_SUMMARY
        npm audit --audit-level=moderate || true
        echo "### Audit Summary" >> $GITHUB_STEP_SUMMARY
        npm audit --audit-level=moderate --json | jq -r '.metadata | "- **Total vulnerabilities:** \(.vulnerabilities.total)\n- **High:** \(.vulnerabilities.high)\n- **Moderate:** \(.vulnerabilities.moderate)\n- **Low:** \(.vulnerabilities.low)"' >> $GITHUB_STEP_SUMMARY || echo "No vulnerabilities found" >> $GITHUB_STEP_SUMMARY

    - name: Generate dependency tree
      run: |
        echo "## Dependency Tree" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        npm list --depth=1 >> $GITHUB_STEP_SUMMARY || true
        echo '```' >> $GITHUB_STEP_SUMMARY

    - name: Check for outdated packages
      run: |
        echo "## Outdated Packages" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY
        npm outdated >> $GITHUB_STEP_SUMMARY || echo "All packages are up to date" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY

  codeql-analysis:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript' ]

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
        queries: security-extended,security-and-quality

    - name: Autobuild
      uses: github/codeql-action/autobuild@v3

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"
