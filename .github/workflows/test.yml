name: React Native Testing

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  lint-and-type-check:
    name: Lint & Type Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 🔍 ESLint
        run: |
          if npm run lint --silent 2>/dev/null; then
            npm run lint
          else
            echo "ESLint script not found - skipping"
          fi

      - name: 🔍 TypeScript check
        run: |
          if [ -f "tsconfig.json" ]; then
            npx tsc --noEmit
          else
            echo "TypeScript config not found - skipping"
          fi

      - name: 🔍 Prettier check
        run: |
          if [ -f ".prettierrc" ] || [ -f "prettier.config.js" ]; then
            npx prettier --check .
          else
            echo "Prettier config not found - skipping"
          fi

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 🧪 Run Jest tests
        run: npm test -- --coverage --watchAll=false

      - name: 📊 Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  metro-bundle:
    name: Metro Bundle Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 📱 Test iOS bundle
        run: npx expo export --platform ios

      - name: 📱 Test Android bundle
        run: npx expo export --platform android

      - name: 🌐 Test Web bundle
        run: npx expo export --platform web

  cross-platform:
    name: Cross-Platform Compatibility
    runs-on: ubuntu-latest
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 🔍 Check platform-specific imports
        run: |
          echo "Checking for proper Platform.OS usage..."
          grep -r "Platform.OS" app/ || echo "No platform-specific code found"
          
      - name: 🔍 Validate native module imports
        run: |
          echo "Checking native module conditional imports..."
          grep -r "require.*native" app/ || echo "No conditional native imports found"
