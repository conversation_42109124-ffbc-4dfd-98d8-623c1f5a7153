name: EAS Build & Deploy

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:
    # Allow manual triggering

jobs:
  build:
    name: EAS Build
    runs-on: ubuntu-latest

    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.x
          cache: npm

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 🚀 Create production build
        run: eas build --platform ios --profile production --non-interactive


