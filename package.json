{"name": "krtr-mesh", "version": "1.0.0", "description": "Decentralized, encrypted, offline-first messaging for the post-platform era", "main": "index.js", "scripts": {"postinstall": "patch-package", "start": "expo start", "start:dev": "expo start --dev-client", "start:tunnel": "expo start --tunnel", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "update": "eas update", "test": "jest", "test:zk": "jest --testPathPattern=zk", "test:mesh": "jest --testPathPattern=mesh", "lint": "eslint .", "format": "prettier --write .", "build:circuits": "./scripts/build-circuits.sh", "setup:noir": "curl -L https://raw.githubusercontent.com/noir-lang/noirup/main/install | bash && noirup", "setup:expo": "./scripts/expo-setup.sh"}, "dependencies": {"@expo/config-plugins": "~10.1.1", "@expo/metro-runtime": "~5.0.4", "@expo/prebuild-config": "~9.0.0", "@noir-lang/backend_barretenberg": "^0.32.0", "@noir-lang/noir_js": "^0.32.0", "@noir-lang/types": "^0.32.0", "@react-native-async-storage/async-storage": "2.1.2", "comlink": "^4.4.1", "expo": "~53.0.0", "expo-crypto": "~14.1.5", "expo-local-authentication": "~16.0.5", "lz4js": "^0.2.0", "metro": "^0.82.0", "metro-config": "^0.82.0", "metro-resolver": "^0.82.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-ble-plx": "^3.1.2", "react-native-keychain": "^8.1.3", "react-native-uuid": "^2.0.1", "react-native-vector-icons": "^10.0.3", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-typescript": "^7.28.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@expo/cli": "latest", "@expo/metro-config": "^0.20.17", "@react-native-community/cli": "^19.1.0", "@react-native/eslint-config": "^0.73.0", "@react-native/metro-config": "^0.74.5", "@react-native/typescript-config": "^0.73.1", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "babel-preset-expo": "~13.0.0", "eslint": "^8.19.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.77.0", "patch-package": "^8.0.0", "prettier": "^2.8.8", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "keywords": ["mesh-networking", "bluetooth", "decentralized", "encrypted-messaging", "offline-first", "peer-to-peer"], "author": "KRTR Team", "license": "MIT", "private": true, "overrides": {"metro": "^0.82.0", "metro-config": "^0.82.0", "metro-resolver": "^0.82.0"}}