# EAS Build ignore file
# Files and directories to exclude from EAS builds

# Development files
.env.local
.env.development
.env.test

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Build artifacts
dist/
build/

# Temporary folders
tmp/
temp/

# Documentation that's not needed for builds
docs/
*.md
!README.md

# Test files
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Development scripts
scripts/
patches/

# Git
.git/
.gitignore
