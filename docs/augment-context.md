# KRTR Mesh - Augment Context Guide

## Project Overview
KRTR Mesh is a decentralized, encrypted, offline-first messaging protocol built with React Native + Expo. This document provides curated knowledge for AI assistants working on the project.

## Development Environment

### Core Stack
- **Platform**: React Native + Expo (bare workflow)
- **iOS**: Xcode with CocoaPods, free Apple Developer account (`<EMAIL>`)
- **Mesh Transport**: Bluetooth Low Energy (react-native-ble-plx)
- **Cryptography**: AES-256-GCM encryption, Ed25519 signatures, X25519 key exchange
- **Zero-Knowledge**: Noir circuits for anonymous authentication
- **Build System**: EAS (Expo Application Services) - limited by free Apple account

### Project Locations
- **Production**: `/Users/<USER>/Desktop/krtr-mesh-production`
- **Local Dev**: `/Users/<USER>/krtr-mesh-local` (used to avoid iCloud sync issues)

### Key Files
- **Startup Script**: `scripts/dev-shell.sh` - comprehensive development environment
- **Activity Log**: `docs/activity.md` - detailed development history
- **Environment**: `.env` contains EXPO_TOKEN and other secrets

## Proven Workflows

### iOS Development - ESTABLISHED PATTERN ✅
**Problem**: EAS cloud builds fail with free Apple Developer accounts
**Solution**: Use direct Xcode builds (proven successful multiple times)

1. **Generate iOS Project**: `npx expo prebuild --platform ios --clean`
2. **Install Dependencies**: `cd ios && pod install` (use COCOAPODS_ARTIFACT_PROXY for speed)
3. **Open Workspace**: `open ios/KRTRMesh.xcworkspace`
4. **Build & Deploy**: ⌘+R in Xcode to connected iPhone
5. **Trust Profile**: Manual trust in iPhone Settings > General > VPN & Device Management

**Critical Notes**:
- Free Apple accounts CANNOT use EAS cloud builds (confirmed multiple times)
- Remove Associated Domains capability for free account compatibility
- Use automatic code signing with Apple ID
- Direct USB installation bypasses Metro network issues

### CocoaPods Optimization
**Speed up installations**:
```bash
COCOAPODS_ARTIFACT_PROXY=https://dl.google.com pod install
```
**Typical install time**: 15 seconds vs. several minutes without proxy

### Common Issues & Resolutions

#### 1. Architecture Mismatch (iOS Simulator)
**Error**: "building for 'iOS-simulator', but linking in object file built for 'iOS'"
**Root Cause**: Static libraries (.a files) built for device being linked in simulator
**Solution**: 
- Remove problematic dependencies (e.g., react-native-sodium)
- Clean: `rm -rf ios/build ios/Pods ios/Podfile.lock`
- Reinstall: `pod install`

#### 2. App Transport Security (ATS) Issues
**Error**: "app transport security policy requires secure connection"
**Solution**: Update `ios/KRTRMesh/Info.plist`:
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSAllowsLocalNetworking</key>
    <true/>
</dict>
```

#### 3. AppRegistry Registration Errors
**Error**: "main has not been registered"
**Root Cause**: Import/export mismatch between App.js and index.js
**Solution**: Ensure consistent component naming:
```javascript
// App.js
export default KRTRMeshApp

// index.js
import KRTRMeshApp from './App'
AppRegistry.registerComponent('main', () => KRTRMeshApp)
```

#### 4. Metro Bundler Network Issues
**Error**: Connection timeouts, "request timed out"
**Solutions**:
- Use LAN mode: `npx expo start --dev-client --lan`
- Ensure same WiFi network for iPhone and Mac
- Use QR code scanning (most reliable)
- Check ATS configuration (see above)

#### 5. Release Build Crashes
**Error**: App crashes immediately on launch
**Root Cause**: Missing JavaScript bundle in Release builds
**Solution**:
1. Export bundle: `npx expo export --platform ios`
2. Copy to iOS: `cp dist/_expo/static/js/ios/*.hbc ios/main.jsbundle`
3. Add bundle to Xcode project resources
4. Rebuild in Release mode

### Git & Repository Management

#### Merge Conflict Patterns
**Common conflicts**: Info.plist, Xcode project files, dependency locks
**Resolution strategy**:
- Keep local URL schemes (krtr, com.zbarber.krtrmesh, exp+krtr-mesh)
- Remove deleted Xcode project files with `git rm`
- Preserve dependency updates in package.json and Podfile.lock

#### Branch Strategy
- **Main branch**: Production-ready code
- **Direct pushes**: Allowed but use descriptive commit messages
- **Activity logging**: Always update docs/activity.md with session details

### Development Dependencies

#### Production vs Development Builds
**Development**: Include expo-dev-client, expo-updates
**Production**: Remove development dependencies for standalone apps
```bash
# Remove dev dependencies
npm uninstall expo-dev-client expo-updates

# Regenerate clean iOS project
npx expo prebuild --platform ios --clean
```

#### Package Management
**Always use package managers** instead of manual file editing:
- `npm install/uninstall` for Node.js dependencies
- `pod install` for iOS native dependencies
- Use `--legacy-peer-deps` for React Native compatibility

### Testing & Quality Assurance

#### Test Execution
- **Unit Tests**: `npm test`
- **ZK Tests**: `npm run test:zk`
- **Mesh Tests**: `npm run test:mesh`
- **Expo Health**: `npx expo doctor`

#### Build Verification
1. Clean builds after major changes
2. Test on physical iPhone (not just simulator)
3. Verify all native modules (BLE, Keychain, Storage)
4. Check Metro bundler connectivity

### Security Considerations

#### Credentials Management
- **EXPO_TOKEN**: Stored in .env file
- **Apple ID**: Free account `<EMAIL>`
- **Code Signing**: Automatic with Apple ID
- **Keychain**: Use react-native-keychain for secure storage

#### Privacy & Encryption
- **End-to-end encryption**: AES-256-GCM for all messages
- **Key exchange**: X25519 for forward secrecy
- **Anonymous auth**: Zero-knowledge proofs via Noir circuits
- **Metadata protection**: Traffic analysis resistance

### Performance Optimization

#### Node.js Settings
```bash
export NODE_OPTIONS="--max-old-space-size=8192"
export WATCHMAN_MAX_FILES=100000
```

#### File Watchers
```bash
ulimit -n 65536  # Increase file descriptor limit
```

#### Build Optimization
- Use Hermes engine for production builds
- Enable New Architecture for React Native
- Minimize bundle size with tree shaking

## Emergency Procedures

### Complete Environment Reset
1. `rm -rf node_modules ios android`
2. `npm install`
3. `npx expo prebuild --platform ios --clean`
4. `cd ios && pod install`
5. Open Xcode workspace and rebuild

### Data Wipe (Security Feature)
- **Triple-tap logo**: Emergency data wipe capability
- **Implementation**: Secure deletion of all local data
- **Recovery**: No recovery possible (by design)

## AI Assistant Guidelines

### When Working on KRTR Mesh:
1. **Always check activity.md** for previous solutions to similar issues
2. **Prefer direct Xcode builds** over EAS for iOS development
3. **Use established patterns** documented in this guide
4. **Update activity.md** with new solutions and patterns
5. **Respect security-first approach** in all recommendations
6. **Consider offline-first operation** in design decisions

### Common User Requests:
- **"Build iOS app"** → Use direct Xcode workflow
- **"Fix build errors"** → Check architecture mismatch patterns
- **"Sync to GitHub"** → Handle merge conflicts carefully
- **"Test on iPhone"** → Use USB connection + Xcode deployment

This guide represents proven solutions from extensive development sessions. Always prioritize security, privacy, and offline-first operation in line with KRTR Mesh's core mission.
