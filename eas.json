{"cli": {"version": ">= 5.9.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "cache": {"disabled": false}, "env": {"NODE_OPTIONS": "--max-old-space-size=4096"}, "prebuildCommand": "npm ci --prefer-offline --no-audit", "ios": {"resourceClass": "m-medium", "cocoapods": "1.15.2"}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "cache": {"disabled": false}, "env": {"NODE_OPTIONS": "--max-old-space-size=4096"}, "prebuildCommand": "npm ci --prefer-offline --no-audit", "ios": {"resourceClass": "m-medium", "cocoapods": "1.15.2"}, "android": {"buildType": "apk"}}, "production": {"cache": {"disabled": false}, "env": {"NODE_OPTIONS": "--max-old-space-size=4096"}, "prebuildCommand": "npm ci --prefer-offline --no-audit", "ios": {"resourceClass": "m-medium", "cocoapods": "1.15.2"}, "android": {"buildType": "app-bundle"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}}