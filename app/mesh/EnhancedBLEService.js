/**
 * KRTR Enhanced BLE Service - High-Performance Bluetooth Low Energy
 * Optimized BLE implementation with connection pooling, adaptive scanning, and power management
 */

import { BleManager } from 'react-native-ble-plx';
import { Platform, AppState } from 'react-native';
import { Buffer } from 'buffer';

// KRTR BLE service configuration
const KRTR_SERVICE_UUID = '6E400001-B5A3-F393-E0A9-E50E24DCCA9E';
const KRTR_CHARACTERISTIC_UUID = '6E400002-B5A3-F393-E0A9-E50E24DCCA9E';

class EnhancedBLEService {
  constructor() {
    this.bleManager = new BleManager();
    this.isInitialized = false;
    this.isScanning = false;
    this.isAdvertising = false;
    
    // Connection management
    this.connectionPool = new Map(); // deviceId -> connection info
    this.maxConnections = Platform.OS === 'ios' ? 8 : 7; // iOS/Android limits
    this.connectionQueue = [];
    this.reconnectAttempts = new Map();
    this.maxReconnectAttempts = 3;
    
    // Adaptive scanning configuration
    this.scanConfig = {
      interval: 1000, // Base scan interval (ms)
      window: 500,    // Scan window (ms)
      mode: 'balanced', // balanced, low_power, high_performance
      adaptiveEnabled: true
    };
    
    // Power management
    this.powerMode = 'balanced'; // low_power, balanced, high_performance
    this.backgroundMode = false;
    this.batteryLevel = 1.0;
    
    // Performance metrics
    this.metrics = {
      connectionsEstablished: 0,
      connectionsLost: 0,
      dataTransmitted: 0,
      dataReceived: 0,
      scanCycles: 0,
      averageRSSI: 0,
      connectionLatency: []
    };
    
    // Event handlers
    this.onDeviceDiscovered = null;
    this.onDeviceConnected = null;
    this.onDeviceDisconnected = null;
    this.onDataReceived = null;
    this.onError = null;
    
    this.setupAppStateHandling();
  }

  // Initialize the enhanced BLE service
  async initialize() {
    try {
      console.log('[Enhanced BLE] Initializing service...');
      
      // Check BLE state
      const state = await this.bleManager.state();
      console.log('[Enhanced BLE] BLE State:', state);
      
      if (state !== 'PoweredOn') {
        throw new Error(`Bluetooth not available. State: ${state}`);
      }
      
      // Setup BLE manager event handlers
      this.setupBLEEventHandlers();
      
      // Initialize adaptive scanning
      this.initializeAdaptiveScanning();
      
      this.isInitialized = true;
      console.log('[Enhanced BLE] Service initialized successfully');
      
      return { initialized: true, state };
    } catch (error) {
      console.error('[Enhanced BLE] Initialization failed:', error);
      throw error;
    }
  }

  // Start optimized scanning with adaptive parameters
  async startScanning() {
    if (!this.isInitialized) {
      throw new Error('BLE service not initialized');
    }
    
    if (this.isScanning) {
      console.log('[Enhanced BLE] Already scanning');
      return;
    }
    
    try {
      const scanOptions = this.getScanOptions();
      
      console.log('[Enhanced BLE] Starting scan with options:', scanOptions);
      
      this.bleManager.startDeviceScan(
        [KRTR_SERVICE_UUID],
        scanOptions,
        this.handleDeviceDiscovered.bind(this)
      );
      
      this.isScanning = true;
      this.metrics.scanCycles++;
      
      // Setup adaptive scan interval adjustment
      if (this.scanConfig.adaptiveEnabled) {
        this.startAdaptiveScanAdjustment();
      }
      
    } catch (error) {
      console.error('[Enhanced BLE] Failed to start scanning:', error);
      throw error;
    }
  }

  // Stop scanning
  async stopScanning() {
    if (!this.isScanning) return;
    
    try {
      this.bleManager.stopDeviceScan();
      this.isScanning = false;
      this.stopAdaptiveScanAdjustment();
      console.log('[Enhanced BLE] Stopped scanning');
    } catch (error) {
      console.error('[Enhanced BLE] Failed to stop scanning:', error);
      throw error;
    }
  }

  // Enhanced device connection with pooling
  async connectToDevice(device) {
    const deviceId = device.id;
    
    // Check if already connected
    if (this.connectionPool.has(deviceId)) {
      const connection = this.connectionPool.get(deviceId);
      if (connection.device.isConnected()) {
        console.log(`[Enhanced BLE] Device ${deviceId} already connected`);
        return connection;
      }
    }
    
    // Check connection limits
    if (this.connectionPool.size >= this.maxConnections) {
      console.log('[Enhanced BLE] Connection pool full, queuing connection');
      return this.queueConnection(device);
    }
    
    try {
      const startTime = Date.now();
      
      console.log(`[Enhanced BLE] Connecting to device: ${deviceId}`);
      
      // Connect with timeout
      const connectedDevice = await device.connect({
        requestMTU: 512, // Request larger MTU for better performance
        timeout: 10000
      });
      
      // Discover services and characteristics
      await connectedDevice.discoverAllServicesAndCharacteristics();
      
      // Get KRTR characteristic
      const characteristic = await connectedDevice.characteristicForService(
        KRTR_SERVICE_UUID,
        KRTR_CHARACTERISTIC_UUID
      );
      
      // Setup connection info
      const connectionInfo = {
        device: connectedDevice,
        characteristic,
        connectedAt: Date.now(),
        lastActivity: Date.now(),
        rssi: device.rssi || -100,
        mtu: connectedDevice.mtu || 23
      };
      
      // Add to connection pool
      this.connectionPool.set(deviceId, connectionInfo);
      
      // Setup data monitoring
      this.setupDataMonitoring(connectionInfo);
      
      // Setup connection monitoring
      this.setupConnectionMonitoring(connectedDevice);
      
      // Update metrics
      const latency = Date.now() - startTime;
      this.metrics.connectionsEstablished++;
      this.metrics.connectionLatency.push(latency);
      
      console.log(`[Enhanced BLE] Connected to ${deviceId} in ${latency}ms, MTU: ${connectionInfo.mtu}`);
      
      // Notify connection
      if (this.onDeviceConnected) {
        this.onDeviceConnected({
          deviceId,
          device: connectedDevice,
          latency,
          mtu: connectionInfo.mtu
        });
      }
      
      // Clear reconnect attempts
      this.reconnectAttempts.delete(deviceId);
      
      return connectionInfo;
      
    } catch (error) {
      console.error(`[Enhanced BLE] Failed to connect to ${deviceId}:`, error);
      
      // Handle reconnection logic
      this.handleConnectionFailure(device, error);
      
      throw error;
    }
  }

  // Send data with automatic fragmentation and retry
  async sendData(deviceId, data) {
    const connection = this.connectionPool.get(deviceId);
    if (!connection) {
      throw new Error(`Device ${deviceId} not connected`);
    }
    
    try {
      // Convert data to buffer if needed
      let dataBuffer;
      if (typeof data === 'string') {
        dataBuffer = Buffer.from(data, 'utf8');
      } else if (Buffer.isBuffer(data)) {
        dataBuffer = data;
      } else {
        dataBuffer = Buffer.from(JSON.stringify(data), 'utf8');
      }
      
      // Check MTU and fragment if necessary
      const maxPayload = connection.mtu - 3; // Account for ATT header
      
      if (dataBuffer.length <= maxPayload) {
        // Send as single packet
        await this.sendSinglePacket(connection, dataBuffer);
      } else {
        // Fragment and send
        await this.sendFragmentedData(connection, dataBuffer, maxPayload);
      }
      
      // Update metrics and activity
      this.metrics.dataTransmitted += dataBuffer.length;
      connection.lastActivity = Date.now();
      
      console.log(`[Enhanced BLE] Sent ${dataBuffer.length} bytes to ${deviceId}`);
      
    } catch (error) {
      console.error(`[Enhanced BLE] Failed to send data to ${deviceId}:`, error);
      throw error;
    }
  }

  // Broadcast data to all connected devices
  async broadcastData(data) {
    const connections = Array.from(this.connectionPool.values());
    if (connections.length === 0) {
      throw new Error('No connected devices for broadcast');
    }
    
    const results = await Promise.allSettled(
      connections.map(connection => 
        this.sendData(connection.device.id, data)
      )
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    console.log(`[Enhanced BLE] Broadcast completed: ${successful} successful, ${failed} failed`);
    
    return { successful, failed, total: connections.length };
  }

  // Get connection pool status
  getConnectionStatus() {
    const connections = Array.from(this.connectionPool.entries()).map(([deviceId, info]) => ({
      deviceId,
      connectedAt: info.connectedAt,
      lastActivity: info.lastActivity,
      rssi: info.rssi,
      mtu: info.mtu,
      isConnected: info.device.isConnected()
    }));
    
    return {
      totalConnections: this.connectionPool.size,
      maxConnections: this.maxConnections,
      queuedConnections: this.connectionQueue.length,
      connections,
      metrics: this.metrics
    };
  }

  // Set power mode for battery optimization
  setPowerMode(mode) {
    this.powerMode = mode;
    
    switch (mode) {
      case 'low_power':
        this.scanConfig.interval = 2000;
        this.scanConfig.window = 200;
        this.maxConnections = Math.min(this.maxConnections, 4);
        break;
        
      case 'balanced':
        this.scanConfig.interval = 1000;
        this.scanConfig.window = 500;
        this.maxConnections = Platform.OS === 'ios' ? 8 : 7;
        break;
        
      case 'high_performance':
        this.scanConfig.interval = 500;
        this.scanConfig.window = 400;
        this.maxConnections = Platform.OS === 'ios' ? 8 : 7;
        break;
    }
    
    console.log(`[Enhanced BLE] Power mode set to: ${mode}`);
    
    // Restart scanning with new parameters if currently scanning
    if (this.isScanning) {
      this.restartScanning();
    }
  }

  // Disconnect from specific device
  async disconnectDevice(deviceId) {
    const connection = this.connectionPool.get(deviceId);
    if (!connection) {
      console.log(`[Enhanced BLE] Device ${deviceId} not in connection pool`);
      return;
    }
    
    try {
      await connection.device.cancelConnection();
      this.connectionPool.delete(deviceId);
      this.reconnectAttempts.delete(deviceId);
      
      console.log(`[Enhanced BLE] Disconnected from ${deviceId}`);
      
      // Process connection queue
      this.processConnectionQueue();
      
    } catch (error) {
      console.error(`[Enhanced BLE] Failed to disconnect from ${deviceId}:`, error);
    }
  }

  // Disconnect from all devices and cleanup
  async disconnect() {
    try {
      console.log('[Enhanced BLE] Disconnecting all devices...');
      
      // Stop scanning
      await this.stopScanning();
      
      // Disconnect all devices
      const disconnectPromises = Array.from(this.connectionPool.keys()).map(
        deviceId => this.disconnectDevice(deviceId)
      );
      
      await Promise.allSettled(disconnectPromises);
      
      // Clear all data structures
      this.connectionPool.clear();
      this.connectionQueue.length = 0;
      this.reconnectAttempts.clear();
      
      console.log('[Enhanced BLE] All devices disconnected');
      
    } catch (error) {
      console.error('[Enhanced BLE] Error during disconnect:', error);
    }
  }

  // Private helper methods
  
  getScanOptions() {
    const baseOptions = {
      allowDuplicates: true,
      scanMode: this.getScanMode(),
      callbackType: 'all'
    };
    
    // Add platform-specific options
    if (Platform.OS === 'android') {
      baseOptions.matchMode = 'aggressive';
      baseOptions.numOfMatches = 'max';
    }
    
    return baseOptions;
  }
  
  getScanMode() {
    switch (this.powerMode) {
      case 'low_power': return 'lowPower';
      case 'balanced': return 'balanced';
      case 'high_performance': return 'lowLatency';
      default: return 'balanced';
    }
  }
  
  handleDeviceDiscovered(error, device) {
    if (error) {
      console.error('[Enhanced BLE] Scan error:', error);
      if (this.onError) this.onError(error);
      return;
    }
    
    if (!device || !device.name || !device.name.startsWith('KRTR-')) {
      return;
    }
    
    console.log(`[Enhanced BLE] Discovered device: ${device.name} (${device.id}), RSSI: ${device.rssi}`);
    
    // Update RSSI metrics
    this.updateRSSIMetrics(device.rssi);
    
    // Notify discovery
    if (this.onDeviceDiscovered) {
      this.onDeviceDiscovered({
        device,
        rssi: device.rssi,
        timestamp: Date.now()
      });
    }
    
    // Auto-connect if we have capacity
    if (this.connectionPool.size < this.maxConnections && !this.connectionPool.has(device.id)) {
      this.connectToDevice(device).catch(error => {
        console.error(`[Enhanced BLE] Auto-connect failed for ${device.id}:`, error);
      });
    }
  }
  
  setupBLEEventHandlers() {
    this.bleManager.onStateChange((state) => {
      console.log('[Enhanced BLE] State changed:', state);
      
      if (state !== 'PoweredOn' && this.isScanning) {
        this.stopScanning();
      }
    });
  }
  
  setupAppStateHandling() {
    AppState.addEventListener('change', (nextAppState) => {
      this.backgroundMode = nextAppState === 'background';
      
      if (this.backgroundMode) {
        // Reduce activity in background
        this.setPowerMode('low_power');
      } else {
        // Resume normal activity
        this.setPowerMode('balanced');
      }
    });
  }
  
  setupDataMonitoring(connectionInfo) {
    connectionInfo.characteristic.monitor((error, characteristic) => {
      if (error) {
        console.error('[Enhanced BLE] Characteristic monitor error:', error);
        return;
      }
      
      try {
        const data = Buffer.from(characteristic.value, 'base64');
        this.metrics.dataReceived += data.length;
        connectionInfo.lastActivity = Date.now();
        
        console.log(`[Enhanced BLE] Received ${data.length} bytes from ${connectionInfo.device.id}`);
        
        if (this.onDataReceived) {
          this.onDataReceived({
            deviceId: connectionInfo.device.id,
            data: data.toString('utf8'),
            bytes: data.length,
            timestamp: Date.now()
          });
        }
        
      } catch (error) {
        console.error('[Enhanced BLE] Data processing error:', error);
      }
    });
  }
  
  setupConnectionMonitoring(device) {
    device.onDisconnected((error, disconnectedDevice) => {
      const deviceId = disconnectedDevice.id;
      console.log(`[Enhanced BLE] Device disconnected: ${deviceId}`);
      
      // Remove from connection pool
      this.connectionPool.delete(deviceId);
      this.metrics.connectionsLost++;
      
      // Notify disconnection
      if (this.onDeviceDisconnected) {
        this.onDeviceDisconnected({
          deviceId,
          error,
          timestamp: Date.now()
        });
      }
      
      // Attempt reconnection if not intentional
      if (!error || error.errorCode !== 'DeviceDisconnected') {
        this.scheduleReconnection(disconnectedDevice);
      }
      
      // Process connection queue
      this.processConnectionQueue();
    });
  }
  
  async sendSinglePacket(connection, data) {
    const base64Data = data.toString('base64');
    await connection.characteristic.writeWithResponse(base64Data);
  }
  
  async sendFragmentedData(connection, data, maxPayload) {
    const fragments = [];
    for (let i = 0; i < data.length; i += maxPayload) {
      fragments.push(data.slice(i, i + maxPayload));
    }
    
    console.log(`[Enhanced BLE] Sending ${fragments.length} fragments`);
    
    for (let i = 0; i < fragments.length; i++) {
      await this.sendSinglePacket(connection, fragments[i]);
      
      // Small delay between fragments to prevent overwhelming
      if (i < fragments.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }
  
  queueConnection(device) {
    return new Promise((resolve, reject) => {
      this.connectionQueue.push({
        device,
        resolve,
        reject,
        timestamp: Date.now()
      });
      
      console.log(`[Enhanced BLE] Queued connection for ${device.id}, queue size: ${this.connectionQueue.length}`);
    });
  }
  
  processConnectionQueue() {
    if (this.connectionQueue.length === 0 || this.connectionPool.size >= this.maxConnections) {
      return;
    }
    
    const queuedConnection = this.connectionQueue.shift();
    if (!queuedConnection) return;
    
    console.log(`[Enhanced BLE] Processing queued connection for ${queuedConnection.device.id}`);
    
    this.connectToDevice(queuedConnection.device)
      .then(queuedConnection.resolve)
      .catch(queuedConnection.reject);
  }
  
  handleConnectionFailure(device, error) {
    const deviceId = device.id;
    const attempts = this.reconnectAttempts.get(deviceId) || 0;
    
    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(deviceId, attempts + 1);
      
      const delay = Math.pow(2, attempts) * 1000; // Exponential backoff
      console.log(`[Enhanced BLE] Scheduling reconnection attempt ${attempts + 1} for ${deviceId} in ${delay}ms`);
      
      setTimeout(() => {
        this.connectToDevice(device).catch(console.error);
      }, delay);
    } else {
      console.log(`[Enhanced BLE] Max reconnection attempts reached for ${deviceId}`);
      this.reconnectAttempts.delete(deviceId);
    }
  }
  
  scheduleReconnection(device) {
    const deviceId = device.id;
    const attempts = this.reconnectAttempts.get(deviceId) || 0;
    
    if (attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(deviceId, attempts + 1);
      
      const delay = 5000 + (attempts * 2000); // Progressive delay
      console.log(`[Enhanced BLE] Scheduling reconnection for ${deviceId} in ${delay}ms`);
      
      setTimeout(() => {
        this.connectToDevice(device).catch(console.error);
      }, delay);
    }
  }
  
  updateRSSIMetrics(rssi) {
    if (this.metrics.averageRSSI === 0) {
      this.metrics.averageRSSI = rssi;
    } else {
      this.metrics.averageRSSI = (this.metrics.averageRSSI * 0.9) + (rssi * 0.1);
    }
  }
  
  initializeAdaptiveScanning() {
    // Adaptive scanning will be implemented based on network conditions
    console.log('[Enhanced BLE] Adaptive scanning initialized');
  }
  
  startAdaptiveScanAdjustment() {
    // Implement adaptive scan parameter adjustment based on discovery rate
    this.adaptiveScanTimer = setInterval(() => {
      this.adjustScanParameters();
    }, 10000); // Adjust every 10 seconds
  }
  
  stopAdaptiveScanAdjustment() {
    if (this.adaptiveScanTimer) {
      clearInterval(this.adaptiveScanTimer);
      this.adaptiveScanTimer = null;
    }
  }
  
  adjustScanParameters() {
    // Implement logic to adjust scan parameters based on:
    // - Discovery rate
    // - Connection success rate
    // - Battery level
    // - Network density
    
    console.log('[Enhanced BLE] Adjusting scan parameters (adaptive)');
  }
  
  async restartScanning() {
    if (this.isScanning) {
      await this.stopScanning();
      await this.startScanning();
    }
  }
}

export default EnhancedBLEService;
