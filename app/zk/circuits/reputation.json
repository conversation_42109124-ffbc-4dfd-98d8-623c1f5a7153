{"noir_version": "1.0.0-beta.6+e796dfd67726cbc28eb9991782533b211025928d", "hash": "7330504321154216376", "abi": {"parameters": [{"name": "message_count", "type": {"kind": "integer", "sign": "unsigned", "width": 32}, "visibility": "private"}, {"name": "positive_ratings", "type": {"kind": "integer", "sign": "unsigned", "width": 32}, "visibility": "private"}, {"name": "negative_ratings", "type": {"kind": "integer", "sign": "unsigned", "width": 32}, "visibility": "private"}, {"name": "secret_salt", "type": {"kind": "field"}, "visibility": "private"}, {"name": "reputation_threshold", "type": {"kind": "integer", "sign": "unsigned", "width": 32}, "visibility": "public"}, {"name": "commitment", "type": {"kind": "field"}, "visibility": "public"}], "return_type": null, "error_types": {"2920182694213909827": {"error_kind": "string", "string": "attempt to subtract with overflow"}}}, "bytecode": "H4sIAAAAAAAA/9VYS26DMBA1hvwgJJG67IZllzbmY+8qddFzhIYcpQfoSXqgXqVSQzOO3AltF4xRGAkNGVsv830YAnaW3em6h/vgdIWgO8mQLeix8R5bBLYIcDi7Fvsfj6DFMJEUWMezVD3uDsWW9sbNxRz0Aid/AQl0JfSYMCWqomjrvJVK7kVuGl2KomwqLbUsdXnItVKtLnRtGlMLIwvVymNp8iNgzYdjKcASC0+FxA031M85cS181TkgjJkTYi3pYhTu7CzZ/+TDiXshIqwXQV4uZNMTuhciW4GOGftJZDG7JrJoIskfSoorwhhjT01BTYor5ocUZ8hPMUiUpMMqm4AQi5Nh7U1IWNcZlV+qzid4KhTN4WU0Mk1Ar21CLJl2hgw5cMunwoTRNeDaUyGpCTChq4UYc1AoSGzsQUlBb2xC7KB0hgw5cIPH6sugpIyuATdsGoOSsmkOCsUT+pTftjbVaIOyBb2zCbGD0hky5AD1oBC8M14GZcvoGnDnqZDUg7Klq4WwH+eYo63PHGrPnfVZT1yd2Pe9p8+P5/e31we8Z4n2xc56SBdPbvETP/jf36KwxM59gtbc3GEJfvnNkf5rL7a7trRnzWLegXb9tXF8AWa5lscJFgAA", "debug_symbols": "tZXLjoMgFIbfhbULDoKgrzKZNNTSiQlRQ7XJpOm7zwHBaRdOGuxs+OXyf8I5XG7kZI7z16Hrz8OFNB83cnSdtd3XwQ6tnrqhx9bbvSCpepicMdhEHvrRNWpn+ok0/WxtQa7azmHQZdR90Ek77KUFMf0JFYHnzhr/dS9+3XTbyiGZOatXu3jZL6COfiF4hr8CFv2VYBl+yVT0S0Fz/CL9X0qZ44dq9astf7XtB84SAHgJWYSSrwRO987hDQSREUdFUx5VWebkoU4TUHwzj36v7UzE34iXMvHyLN6ByMqFTGe6pptnGuT+WMr9sZT7Yyn/M5a1xy+xzLqfgLIEAJpJUGUiAM254wEEXQnyOQqfWNNt557eNcJxZEFEKKtQylAq0uDOrUMJlDQ4MYBF2CLlIghgfgwimK9XUWVUFdWjcJ8wGhWisqiIYwKVRxVRPc8v5Kpdp4/WxNf4PPftw+M8fY+pJz3foxtac5qd8QsOfRiCHw==", "file_map": {"50": {"source": "// KRTR Private Reputation Circuit\n// Proves good reputation without revealing interaction history\n\nfn main(\n    // Private inputs (not revealed)\n    message_count: u32,\n    positive_ratings: u32,\n    negative_ratings: u32,\n    secret_salt: Field,\n\n    // Public inputs (revealed)\n    reputation_threshold: pub u32,\n    commitment: pub Field\n) {\n    // 1. Verify minimum message count (active user)\n    assert(message_count >= 10);\n\n    // 2. Calculate reputation score (positive - negative)\n    let reputation_score = positive_ratings - negative_ratings;\n\n    // 3. Verify reputation meets threshold\n    assert(reputation_score >= reputation_threshold);\n\n    // 4. Verify commitment to private data\n    let hash1 = hash_simple(message_count as Field, positive_ratings as Field);\n    let hash2 = hash_simple(negative_ratings as Field, secret_salt);\n    let computed_commitment = hash_simple(hash1, hash2);\n    assert(computed_commitment == commitment);\n\n    // 5. Ensure reasonable bounds (prevent overflow attacks)\n    assert(message_count < 1000000); // Max 1M messages\n    assert(positive_ratings < 1000000); // Max 1M positive ratings\n    assert(negative_ratings < 100000); // Max 100K negative ratings\n}\n\n// Simple hash function for demonstration\nfn hash_simple(a: Field, b: Field) -> Field {\n    // Use a simple but secure hash based on field arithmetic\n    // The modulus is automatically handled by the Field type\n    a * 7 + b * 13 + 42\n}\n", "path": "/Users/<USER>/Desktop/AAA Shibumi Crypto/AAA_KRTR_MESH/krtr-mesh/circuits/reputation/src/main.nr"}}, "names": ["main"], "brillig_names": ["directive_integer_quotient"]}