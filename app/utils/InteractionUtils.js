/**
 * KRTR Mesh - Interaction Manager Utilities
 * Ensures smooth UI interactions by deferring heavy operations
 */

import { InteractionManager } from 'react-native';

/**
 * Wraps heavy operations to run after interactions complete
 * Prevents UI blocking during animations and user interactions
 */
export const runAfterInteractions = (operation) => {
  return new Promise((resolve, reject) => {
    InteractionManager.runAfterInteractions(() => {
      try {
        const result = operation();
        if (result && typeof result.then === 'function') {
          // Handle async operations
          result.then(resolve).catch(reject);
        } else {
          // Handle sync operations
          resolve(result);
        }
      } catch (error) {
        reject(error);
      }
    });
  });
};

/**
 * Wraps service initialization to prevent UI blocking
 */
export const initializeServiceAfterInteractions = async (serviceInitializer) => {
  return runAfterInteractions(serviceInitializer);
};

/**
 * Wraps Bluetooth operations that might block UI
 */
export const performBluetoothOperationAfterInteractions = async (operation) => {
  return runAfterInteractions(operation);
};

/**
 * Wraps encryption operations that might be CPU intensive
 */
export const performEncryptionAfterInteractions = async (operation) => {
  return runAfterInteractions(operation);
};

/**
 * Wraps ZK proof generation (very CPU intensive)
 */
export const generateZKProofAfterInteractions = async (proofGenerator) => {
  return runAfterInteractions(proofGenerator);
};

/**
 * Batches multiple UI updates to run after interactions
 */
export const batchUIUpdatesAfterInteractions = (updates) => {
  return runAfterInteractions(() => {
    updates.forEach(update => update());
  });
};

/**
 * Creates a handle for cancelling deferred operations
 */
export const createCancellableInteraction = (operation) => {
  let cancelled = false;
  
  const promise = new Promise((resolve, reject) => {
    InteractionManager.runAfterInteractions(() => {
      if (cancelled) {
        reject(new Error('Operation cancelled'));
        return;
      }
      
      try {
        const result = operation();
        if (result && typeof result.then === 'function') {
          result.then(resolve).catch(reject);
        } else {
          resolve(result);
        }
      } catch (error) {
        reject(error);
      }
    });
  });
  
  return {
    promise,
    cancel: () => { cancelled = true; }
  };
};

/**
 * Utility for mesh networking operations that should not block UI
 */
export const deferMeshOperation = (operation, priority = 'normal') => {
  // For high priority operations, run immediately
  if (priority === 'high') {
    return operation();
  }
  
  // For normal/low priority, defer after interactions
  return runAfterInteractions(operation);
};

export default {
  runAfterInteractions,
  initializeServiceAfterInteractions,
  performBluetoothOperationAfterInteractions,
  performEncryptionAfterInteractions,
  generateZKProofAfterInteractions,
  batchUIUpdatesAfterInteractions,
  createCancellableInteraction,
  deferMeshOperation,
};
