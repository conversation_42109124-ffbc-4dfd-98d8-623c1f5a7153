/**
 * KRTR MultipeerConnectivity Service - React Native Interface
 * High-performance iOS peer discovery and messaging using MultipeerConnectivity
 */

import { NativeModules, NativeEventEmitter, Platform } from 'react-native';

const { KRTRMultipeerConnectivity } = NativeModules;

class MultipeerConnectivityService {
  constructor() {
    this.isAvailable = Platform.OS === 'ios' && KRTRMultipeerConnectivity;
    this.eventEmitter = this.isAvailable ? new NativeEventEmitter(KRTRMultipeerConnectivity) : null;
    this.listeners = new Map();
    this.isInitialized = false;
    this.isAdvertising = false;
    this.isBrowsing = false;
    
    // Event handlers
    this.onPeerDiscovered = null;
    this.onPeerLost = null;
    this.onPeerConnected = null;
    this.onPeerDisconnected = null;
    this.onDataReceived = null;
    this.onConnectionStateChanged = null;
    this.onError = null;
    
    if (this.isAvailable) {
      this.setupEventListeners();
    }
  }

  // Initialize the MultipeerConnectivity service
  async initialize() {
    if (!this.isAvailable) {
      throw new Error('MultipeerConnectivity is only available on iOS');
    }

    try {
      const result = await KRTRMultipeerConnectivity.initialize();
      this.isInitialized = true;
      console.log('[MultipeerConnectivity] Initialized:', result);
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Initialization failed:', error);
      throw error;
    }
  }

  // Start advertising this device to nearby peers
  async startAdvertising() {
    this.ensureInitialized();
    
    try {
      const result = await KRTRMultipeerConnectivity.startAdvertising();
      this.isAdvertising = true;
      console.log('[MultipeerConnectivity] Started advertising');
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to start advertising:', error);
      throw error;
    }
  }

  // Stop advertising this device
  async stopAdvertising() {
    if (!this.isAdvertising) return { advertising: false };
    
    try {
      const result = await KRTRMultipeerConnectivity.stopAdvertising();
      this.isAdvertising = false;
      console.log('[MultipeerConnectivity] Stopped advertising');
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to stop advertising:', error);
      throw error;
    }
  }

  // Start browsing for nearby peers
  async startBrowsing() {
    this.ensureInitialized();
    
    try {
      const result = await KRTRMultipeerConnectivity.startBrowsing();
      this.isBrowsing = true;
      console.log('[MultipeerConnectivity] Started browsing');
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to start browsing:', error);
      throw error;
    }
  }

  // Stop browsing for peers
  async stopBrowsing() {
    if (!this.isBrowsing) return { browsing: false };
    
    try {
      const result = await KRTRMultipeerConnectivity.stopBrowsing();
      this.isBrowsing = false;
      console.log('[MultipeerConnectivity] Stopped browsing');
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to stop browsing:', error);
      throw error;
    }
  }

  // Send data to a specific peer
  async sendData(data, peerName) {
    this.ensureInitialized();
    
    if (typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    
    try {
      const result = await KRTRMultipeerConnectivity.sendData(data, peerName);
      console.log(`[MultipeerConnectivity] Sent data to ${peerName}:`, result);
      return result;
    } catch (error) {
      console.error(`[MultipeerConnectivity] Failed to send data to ${peerName}:`, error);
      throw error;
    }
  }

  // Broadcast data to all connected peers
  async broadcastData(data) {
    this.ensureInitialized();
    
    if (typeof data !== 'string') {
      data = JSON.stringify(data);
    }
    
    try {
      const result = await KRTRMultipeerConnectivity.broadcastData(data);
      console.log('[MultipeerConnectivity] Broadcast data:', result);
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to broadcast data:', error);
      throw error;
    }
  }

  // Get list of connected peers
  async getConnectedPeers() {
    this.ensureInitialized();
    
    try {
      const result = await KRTRMultipeerConnectivity.getConnectedPeers();
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to get connected peers:', error);
      throw error;
    }
  }

  // Disconnect from all peers and stop services
  async disconnect() {
    if (!this.isInitialized) return { disconnected: true };
    
    try {
      const result = await KRTRMultipeerConnectivity.disconnect();
      this.isInitialized = false;
      this.isAdvertising = false;
      this.isBrowsing = false;
      console.log('[MultipeerConnectivity] Disconnected');
      return result;
    } catch (error) {
      console.error('[MultipeerConnectivity] Failed to disconnect:', error);
      throw error;
    }
  }

  // Start both advertising and browsing
  async startMeshNetworking() {
    await this.initialize();
    await Promise.all([
      this.startAdvertising(),
      this.startBrowsing()
    ]);
    console.log('[MultipeerConnectivity] Mesh networking started');
  }

  // Stop all mesh networking activities
  async stopMeshNetworking() {
    await Promise.all([
      this.stopAdvertising(),
      this.stopBrowsing()
    ]);
    console.log('[MultipeerConnectivity] Mesh networking stopped');
  }

  // Event listener management
  addEventListener(eventType, callback) {
    if (!this.isAvailable) return null;
    
    const listener = this.eventEmitter.addListener(eventType, callback);
    this.listeners.set(eventType, listener);
    return listener;
  }

  removeEventListener(eventType) {
    if (!this.isAvailable) return;
    
    const listener = this.listeners.get(eventType);
    if (listener) {
      listener.remove();
      this.listeners.delete(eventType);
    }
  }

  removeAllEventListeners() {
    if (!this.isAvailable) return;
    
    this.listeners.forEach(listener => listener.remove());
    this.listeners.clear();
  }

  // Setup default event listeners
  setupEventListeners() {
    if (!this.isAvailable) return;

    this.addEventListener('onPeerDiscovered', (event) => {
      console.log('[MultipeerConnectivity] Peer discovered:', event);
      if (this.onPeerDiscovered) this.onPeerDiscovered(event);
    });

    this.addEventListener('onPeerLost', (event) => {
      console.log('[MultipeerConnectivity] Peer lost:', event);
      if (this.onPeerLost) this.onPeerLost(event);
    });

    this.addEventListener('onPeerConnected', (event) => {
      console.log('[MultipeerConnectivity] Peer connected:', event);
      if (this.onPeerConnected) this.onPeerConnected(event);
    });

    this.addEventListener('onPeerDisconnected', (event) => {
      console.log('[MultipeerConnectivity] Peer disconnected:', event);
      if (this.onPeerDisconnected) this.onPeerDisconnected(event);
    });

    this.addEventListener('onDataReceived', (event) => {
      console.log('[MultipeerConnectivity] Data received:', event);
      if (this.onDataReceived) this.onDataReceived(event);
    });

    this.addEventListener('onConnectionStateChanged', (event) => {
      console.log('[MultipeerConnectivity] Connection state changed:', event);
      if (this.onConnectionStateChanged) this.onConnectionStateChanged(event);
    });

    this.addEventListener('onError', (event) => {
      console.error('[MultipeerConnectivity] Error:', event);
      if (this.onError) this.onError(event);
    });
  }

  // Utility methods
  ensureInitialized() {
    if (!this.isAvailable) {
      throw new Error('MultipeerConnectivity is only available on iOS');
    }
    if (!this.isInitialized) {
      throw new Error('MultipeerConnectivity not initialized. Call initialize() first.');
    }
  }

  // Get service status
  getStatus() {
    return {
      available: this.isAvailable,
      initialized: this.isInitialized,
      advertising: this.isAdvertising,
      browsing: this.isBrowsing,
      platform: Platform.OS
    };
  }

  // Cleanup
  destroy() {
    this.removeAllEventListeners();
    if (this.isInitialized) {
      this.disconnect().catch(console.error);
    }
  }
}

// Export singleton instance
export default new MultipeerConnectivityService();
