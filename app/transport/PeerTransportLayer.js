/**
 * KRTR Peer Transport Layer - Unified Transport Abstraction
 * Provides a unified interface for different transport mechanisms:
 * - Bluetooth Low Energy (BLE)
 * - iOS MultipeerConnectivity
 * - Android WiFi Direct (future)
 * - WebRTC (future)
 */

import { Platform } from 'react-native';
import EnhancedBLEService from '../mesh/EnhancedBLEService';
import MultipeerConnectivityService from '../native/MultipeerConnectivityService';

// Transport types
export const TransportType = {
  BLE: 'ble',
  MULTIPEER_CONNECTIVITY: 'multipeer',
  WIFI_DIRECT: 'wifi_direct',
  WEBRTC: 'webrtc'
};

// Transport capabilities
export const TransportCapability = {
  PEER_DISCOVERY: 'peer_discovery',
  DIRECT_MESSAGING: 'direct_messaging',
  BROADCAST_MESSAGING: 'broadcast_messaging',
  FILE_TRANSFER: 'file_transfer',
  HIGH_THROUGHPUT: 'high_throughput',
  LOW_LATENCY: 'low_latency',
  BACKGROUND_OPERATION: 'background_operation'
};

class PeerTransportLayer {
  constructor() {
    this.transports = new Map();
    this.activeTransports = new Set();
    this.primaryTransport = null;
    this.fallbackTransports = [];
    
    // Transport preferences by platform
    this.transportPreferences = this.getTransportPreferences();
    
    // Unified event handlers
    this.onPeerDiscovered = null;
    this.onPeerConnected = null;
    this.onPeerDisconnected = null;
    this.onDataReceived = null;
    this.onTransportStateChanged = null;
    this.onError = null;
    
    // Performance metrics
    this.metrics = {
      totalPeersDiscovered: 0,
      totalConnections: 0,
      totalDataTransmitted: 0,
      totalDataReceived: 0,
      transportSwitches: 0,
      averageLatency: 0
    };
    
    this.initializeTransports();
  }

  // Initialize available transports based on platform
  initializeTransports() {
    console.log('[PeerTransportLayer] Initializing transports for platform:', Platform.OS);
    
    // Initialize BLE transport (available on all platforms)
    this.registerTransport(TransportType.BLE, new EnhancedBLEService(), {
      capabilities: [
        TransportCapability.PEER_DISCOVERY,
        TransportCapability.DIRECT_MESSAGING,
        TransportCapability.BROADCAST_MESSAGING,
        TransportCapability.BACKGROUND_OPERATION
      ],
      maxConnections: Platform.OS === 'ios' ? 8 : 7,
      maxThroughput: 1000, // bytes/sec
      averageLatency: 100, // ms
      powerConsumption: 'medium',
      reliability: 0.85
    });
    
    // Initialize MultipeerConnectivity transport (iOS only)
    if (Platform.OS === 'ios' && MultipeerConnectivityService.isAvailable) {
      this.registerTransport(TransportType.MULTIPEER_CONNECTIVITY, MultipeerConnectivityService, {
        capabilities: [
          TransportCapability.PEER_DISCOVERY,
          TransportCapability.DIRECT_MESSAGING,
          TransportCapability.BROADCAST_MESSAGING,
          TransportCapability.HIGH_THROUGHPUT,
          TransportCapability.LOW_LATENCY
        ],
        maxConnections: 8,
        maxThroughput: 10000, // bytes/sec
        averageLatency: 20, // ms
        powerConsumption: 'high',
        reliability: 0.95
      });
    }
    
    // Set primary and fallback transports based on preferences
    this.configurePrimaryTransport();
  }

  // Register a transport with its capabilities
  registerTransport(type, transport, config) {
    const transportInfo = {
      type,
      transport,
      config,
      isActive: false,
      isInitialized: false,
      lastError: null,
      metrics: {
        connectionsEstablished: 0,
        dataTransmitted: 0,
        dataReceived: 0,
        errors: 0
      }
    };
    
    this.transports.set(type, transportInfo);
    this.setupTransportEventHandlers(transportInfo);
    
    console.log(`[PeerTransportLayer] Registered transport: ${type}`);
  }

  // Configure primary transport based on platform preferences
  configurePrimaryTransport() {
    for (const preferredType of this.transportPreferences.primary) {
      if (this.transports.has(preferredType)) {
        this.primaryTransport = preferredType;
        break;
      }
    }
    
    // Set fallback transports
    this.fallbackTransports = this.transportPreferences.fallback.filter(
      type => this.transports.has(type) && type !== this.primaryTransport
    );
    
    console.log(`[PeerTransportLayer] Primary transport: ${this.primaryTransport}`);
    console.log(`[PeerTransportLayer] Fallback transports: ${this.fallbackTransports.join(', ')}`);
  }

  // Initialize the transport layer
  async initialize() {
    console.log('[PeerTransportLayer] Initializing transport layer...');
    
    try {
      // Initialize primary transport first
      if (this.primaryTransport) {
        await this.initializeTransport(this.primaryTransport);
      }
      
      // Initialize fallback transports
      for (const transportType of this.fallbackTransports) {
        try {
          await this.initializeTransport(transportType);
        } catch (error) {
          console.warn(`[PeerTransportLayer] Failed to initialize fallback transport ${transportType}:`, error);
        }
      }
      
      console.log('[PeerTransportLayer] Transport layer initialized');
      return { initialized: true, primaryTransport: this.primaryTransport };
      
    } catch (error) {
      console.error('[PeerTransportLayer] Failed to initialize transport layer:', error);
      throw error;
    }
  }

  // Initialize a specific transport
  async initializeTransport(transportType) {
    const transportInfo = this.transports.get(transportType);
    if (!transportInfo) {
      throw new Error(`Transport ${transportType} not registered`);
    }
    
    if (transportInfo.isInitialized) {
      console.log(`[PeerTransportLayer] Transport ${transportType} already initialized`);
      return;
    }
    
    try {
      console.log(`[PeerTransportLayer] Initializing transport: ${transportType}`);
      
      await transportInfo.transport.initialize();
      transportInfo.isInitialized = true;
      transportInfo.lastError = null;
      
      console.log(`[PeerTransportLayer] Transport ${transportType} initialized successfully`);
      
    } catch (error) {
      transportInfo.lastError = error;
      console.error(`[PeerTransportLayer] Failed to initialize transport ${transportType}:`, error);
      throw error;
    }
  }

  // Start mesh networking on all available transports
  async startMeshNetworking() {
    console.log('[PeerTransportLayer] Starting mesh networking...');
    
    const results = [];
    
    // Start primary transport first
    if (this.primaryTransport) {
      try {
        await this.startTransport(this.primaryTransport);
        results.push({ transport: this.primaryTransport, status: 'started' });
      } catch (error) {
        console.error(`[PeerTransportLayer] Failed to start primary transport ${this.primaryTransport}:`, error);
        results.push({ transport: this.primaryTransport, status: 'failed', error });
        
        // Try fallback transports if primary fails
        await this.handlePrimaryTransportFailure();
      }
    }
    
    // Start fallback transports for redundancy
    for (const transportType of this.fallbackTransports) {
      try {
        await this.startTransport(transportType);
        results.push({ transport: transportType, status: 'started' });
      } catch (error) {
        console.warn(`[PeerTransportLayer] Failed to start fallback transport ${transportType}:`, error);
        results.push({ transport: transportType, status: 'failed', error });
      }
    }
    
    console.log('[PeerTransportLayer] Mesh networking started:', results);
    return results;
  }

  // Start a specific transport
  async startTransport(transportType) {
    const transportInfo = this.transports.get(transportType);
    if (!transportInfo) {
      throw new Error(`Transport ${transportType} not registered`);
    }
    
    if (!transportInfo.isInitialized) {
      await this.initializeTransport(transportType);
    }
    
    if (transportInfo.isActive) {
      console.log(`[PeerTransportLayer] Transport ${transportType} already active`);
      return;
    }
    
    try {
      console.log(`[PeerTransportLayer] Starting transport: ${transportType}`);
      
      // Start transport-specific networking
      if (transportType === TransportType.BLE) {
        await transportInfo.transport.startScanning();
      } else if (transportType === TransportType.MULTIPEER_CONNECTIVITY) {
        await transportInfo.transport.startMeshNetworking();
      }
      
      transportInfo.isActive = true;
      this.activeTransports.add(transportType);
      
      console.log(`[PeerTransportLayer] Transport ${transportType} started successfully`);
      
      if (this.onTransportStateChanged) {
        this.onTransportStateChanged({
          transport: transportType,
          state: 'active',
          timestamp: Date.now()
        });
      }
      
    } catch (error) {
      transportInfo.lastError = error;
      transportInfo.metrics.errors++;
      console.error(`[PeerTransportLayer] Failed to start transport ${transportType}:`, error);
      throw error;
    }
  }

  // Send data using the best available transport
  async sendData(peerId, data) {
    const availableTransports = this.getAvailableTransportsForPeer(peerId);
    
    if (availableTransports.length === 0) {
      throw new Error(`No available transports for peer: ${peerId}`);
    }
    
    // Try transports in order of preference
    for (const transportType of availableTransports) {
      try {
        const transportInfo = this.transports.get(transportType);
        const result = await transportInfo.transport.sendData(peerId, data);
        
        // Update metrics
        const dataSize = typeof data === 'string' ? data.length : JSON.stringify(data).length;
        transportInfo.metrics.dataTransmitted += dataSize;
        this.metrics.totalDataTransmitted += dataSize;
        
        console.log(`[PeerTransportLayer] Data sent via ${transportType} to ${peerId}`);
        return { transport: transportType, result };
        
      } catch (error) {
        console.warn(`[PeerTransportLayer] Failed to send data via ${transportType}:`, error);
        continue;
      }
    }
    
    throw new Error(`Failed to send data to ${peerId} on all available transports`);
  }

  // Broadcast data using all available transports
  async broadcastData(data) {
    const results = [];
    
    for (const transportType of this.activeTransports) {
      try {
        const transportInfo = this.transports.get(transportType);
        const result = await transportInfo.transport.broadcastData(data);
        
        // Update metrics
        const dataSize = typeof data === 'string' ? data.length : JSON.stringify(data).length;
        transportInfo.metrics.dataTransmitted += dataSize;
        this.metrics.totalDataTransmitted += dataSize;
        
        results.push({ transport: transportType, status: 'success', result });
        
      } catch (error) {
        console.warn(`[PeerTransportLayer] Broadcast failed on ${transportType}:`, error);
        results.push({ transport: transportType, status: 'failed', error });
      }
    }
    
    console.log('[PeerTransportLayer] Broadcast completed:', results);
    return results;
  }

  // Get connected peers from all transports
  async getConnectedPeers() {
    const allPeers = new Map();
    
    for (const transportType of this.activeTransports) {
      try {
        const transportInfo = this.transports.get(transportType);
        let peers;
        
        if (transportType === TransportType.BLE) {
          const status = transportInfo.transport.getConnectionStatus();
          peers = status.connections.map(conn => ({
            id: conn.deviceId,
            transport: transportType,
            ...conn
          }));
        } else if (transportType === TransportType.MULTIPEER_CONNECTIVITY) {
          const result = await transportInfo.transport.getConnectedPeers();
          peers = result.peers.map(peer => ({
            id: peer.id,
            transport: transportType,
            ...peer
          }));
        }
        
        // Merge peers (same peer might be connected via multiple transports)
        peers.forEach(peer => {
          if (allPeers.has(peer.id)) {
            const existing = allPeers.get(peer.id);
            existing.transports.push(transportType);
          } else {
            allPeers.set(peer.id, {
              ...peer,
              transports: [transportType]
            });
          }
        });
        
      } catch (error) {
        console.warn(`[PeerTransportLayer] Failed to get peers from ${transportType}:`, error);
      }
    }
    
    return Array.from(allPeers.values());
  }

  // Get transport layer status
  getStatus() {
    const transportStatus = {};
    
    for (const [type, info] of this.transports) {
      transportStatus[type] = {
        isActive: info.isActive,
        isInitialized: info.isInitialized,
        lastError: info.lastError?.message,
        metrics: info.metrics,
        config: info.config
      };
    }
    
    return {
      primaryTransport: this.primaryTransport,
      activeTransports: Array.from(this.activeTransports),
      transports: transportStatus,
      metrics: this.metrics
    };
  }

  // Stop all transports
  async stopMeshNetworking() {
    console.log('[PeerTransportLayer] Stopping mesh networking...');
    
    const results = [];
    
    for (const transportType of this.activeTransports) {
      try {
        await this.stopTransport(transportType);
        results.push({ transport: transportType, status: 'stopped' });
      } catch (error) {
        console.warn(`[PeerTransportLayer] Failed to stop transport ${transportType}:`, error);
        results.push({ transport: transportType, status: 'failed', error });
      }
    }
    
    this.activeTransports.clear();
    
    console.log('[PeerTransportLayer] Mesh networking stopped:', results);
    return results;
  }

  // Private helper methods
  
  getTransportPreferences() {
    if (Platform.OS === 'ios') {
      return {
        primary: [TransportType.MULTIPEER_CONNECTIVITY, TransportType.BLE],
        fallback: [TransportType.BLE, TransportType.MULTIPEER_CONNECTIVITY]
      };
    } else {
      return {
        primary: [TransportType.BLE],
        fallback: []
      };
    }
  }
  
  setupTransportEventHandlers(transportInfo) {
    const { type, transport } = transportInfo;
    
    // Setup event handlers based on transport type
    if (type === TransportType.BLE) {
      transport.onDeviceDiscovered = (event) => this.handlePeerDiscovered(type, event);
      transport.onDeviceConnected = (event) => this.handlePeerConnected(type, event);
      transport.onDeviceDisconnected = (event) => this.handlePeerDisconnected(type, event);
      transport.onDataReceived = (event) => this.handleDataReceived(type, event);
      transport.onError = (error) => this.handleTransportError(type, error);
      
    } else if (type === TransportType.MULTIPEER_CONNECTIVITY) {
      transport.onPeerDiscovered = (event) => this.handlePeerDiscovered(type, event);
      transport.onPeerConnected = (event) => this.handlePeerConnected(type, event);
      transport.onPeerDisconnected = (event) => this.handlePeerDisconnected(type, event);
      transport.onDataReceived = (event) => this.handleDataReceived(type, event);
      transport.onError = (error) => this.handleTransportError(type, error);
    }
  }
  
  handlePeerDiscovered(transportType, event) {
    this.metrics.totalPeersDiscovered++;
    
    console.log(`[PeerTransportLayer] Peer discovered via ${transportType}:`, event);
    
    if (this.onPeerDiscovered) {
      this.onPeerDiscovered({
        ...event,
        transport: transportType,
        timestamp: Date.now()
      });
    }
  }
  
  handlePeerConnected(transportType, event) {
    const transportInfo = this.transports.get(transportType);
    transportInfo.metrics.connectionsEstablished++;
    this.metrics.totalConnections++;
    
    console.log(`[PeerTransportLayer] Peer connected via ${transportType}:`, event);
    
    if (this.onPeerConnected) {
      this.onPeerConnected({
        ...event,
        transport: transportType,
        timestamp: Date.now()
      });
    }
  }
  
  handlePeerDisconnected(transportType, event) {
    console.log(`[PeerTransportLayer] Peer disconnected via ${transportType}:`, event);
    
    if (this.onPeerDisconnected) {
      this.onPeerDisconnected({
        ...event,
        transport: transportType,
        timestamp: Date.now()
      });
    }
  }
  
  handleDataReceived(transportType, event) {
    const transportInfo = this.transports.get(transportType);
    transportInfo.metrics.dataReceived += event.bytes || 0;
    this.metrics.totalDataReceived += event.bytes || 0;
    
    console.log(`[PeerTransportLayer] Data received via ${transportType}:`, event);
    
    if (this.onDataReceived) {
      this.onDataReceived({
        ...event,
        transport: transportType,
        timestamp: Date.now()
      });
    }
  }
  
  handleTransportError(transportType, error) {
    const transportInfo = this.transports.get(transportType);
    transportInfo.metrics.errors++;
    transportInfo.lastError = error;
    
    console.error(`[PeerTransportLayer] Transport error on ${transportType}:`, error);
    
    if (this.onError) {
      this.onError({
        transport: transportType,
        error,
        timestamp: Date.now()
      });
    }
  }
  
  async handlePrimaryTransportFailure() {
    console.log('[PeerTransportLayer] Primary transport failed, switching to fallback...');
    
    for (const fallbackType of this.fallbackTransports) {
      try {
        await this.startTransport(fallbackType);
        this.primaryTransport = fallbackType;
        this.metrics.transportSwitches++;
        
        console.log(`[PeerTransportLayer] Switched to fallback transport: ${fallbackType}`);
        break;
        
      } catch (error) {
        console.warn(`[PeerTransportLayer] Fallback transport ${fallbackType} also failed:`, error);
      }
    }
  }
  
  getAvailableTransportsForPeer(peerId) {
    // For now, return all active transports
    // In the future, this could be optimized based on peer capabilities
    return Array.from(this.activeTransports);
  }
  
  async stopTransport(transportType) {
    const transportInfo = this.transports.get(transportType);
    if (!transportInfo || !transportInfo.isActive) {
      return;
    }
    
    try {
      if (transportType === TransportType.BLE) {
        await transportInfo.transport.disconnect();
      } else if (transportType === TransportType.MULTIPEER_CONNECTIVITY) {
        await transportInfo.transport.stopMeshNetworking();
      }
      
      transportInfo.isActive = false;
      this.activeTransports.delete(transportType);
      
      console.log(`[PeerTransportLayer] Transport ${transportType} stopped`);
      
    } catch (error) {
      console.error(`[PeerTransportLayer] Failed to stop transport ${transportType}:`, error);
      throw error;
    }
  }
}

export default PeerTransportLayer;
