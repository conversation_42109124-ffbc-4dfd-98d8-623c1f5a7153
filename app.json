{
  "expo": {
    "name": "KRTR Mesh",
    "slug": "krtr-mesh",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "dark",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#1a1a1a"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.zbarber.krtrmesh",
      "buildNumber": "1",
      "deploymentTarget": "17.0",
      "infoPlist": {
        "NSBluetoothAlwaysUsageDescription": "KRTR needs Bluetooth to connect with nearby devices for secure mesh networking",
        "NSBluetoothPeripheralUsageDescription": "KRTR needs Bluetooth to advertise to nearby devices for mesh networking",
        "NSLocalNetworkUsageDescription": "KRTR uses local networking for mesh communication",
        "UIBackgroundModes": [
          "bluetooth-central",
          "bluetooth-peripheral",
          "bluetooth-central",
          "bluetooth-peripheral",
          "bluetooth-central",
          "bluetooth-peripheral",
          "bluetooth-central",
          "bluetooth-peripheral"
        ],
        "NSAppTransportSecurity": {
          "NSAllowsArbitraryLoads": false,
          "NSExceptionDomains": {}
        },
        "NSFaceIDUsageDescription": "Allow KRTR to use Face ID for secure access to your mesh identity",
        "ITSAppUsesNonExemptEncryption": false
      },

    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#1a1a1a"
      },
      "package": "com.zbarber.krtrmesh",
      "versionCode": 1,
      "permissions": [
        "android.permission.BLUETOOTH",
        "android.permission.BLUETOOTH_ADMIN",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.BLUETOOTH_SCAN",
        "android.permission.BLUETOOTH_ADVERTISE",
        "android.permission.BLUETOOTH_CONNECT",
        "android.permission.WAKE_LOCK",
        "android.permission.FOREGROUND_SERVICE",
        "android.permission.USE_BIOMETRIC",
        "android.permission.USE_FINGERPRINT"
      ],
      "intentFilters": [
        {
          "action": "VIEW",
          "data": [
            {
              "scheme": "https",
              "host": "krtr.mesh"
            }
          ],
          "category": [
            "BROWSABLE",
            "DEFAULT"
          ]
        }
      ]
    },
    "web": {
      "favicon": "./assets/favicon.png",
      "bundler": "metro"
    },
    "plugins": [
      [
        "expo-local-authentication",
        {
          "faceIDPermission": "Allow KRTR to use Face ID for secure access to your mesh identity"
        }
      ]
    ],
    "scheme": "krtr",
    "extra": {
      "eas": {
        "projectId": "aeeba0cd-2e0e-46aa-99c7-6e63472094b0"
      }
    },
    "owner": "z0rlord"
  }
}
